"use client";

import type React from "react";
import { useState } from "react";
import { AppSidebar } from "@/components/app-sidebar";
import { AppTopbar } from "@/components/app-topbar";

export default function AppLayout({ children }: { children: React.ReactNode }) {
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);

  // Authentication is now handled by middleware
  // This layout only renders for authenticated users

  return (
    <div className="flex min-h-screen">
      {/* Desktop Sidebar - Only visible on md+ screens */}
      <div
        className={`hidden md:block fixed left-0 top-0 h-screen z-30 transition-all duration-300 ${
          isSidebarCollapsed ? "w-16" : "w-64"
        }`}
      >
        <AppSidebar
          isCollapsed={isSidebarCollapsed}
          onCollapseChange={setIsSidebarCollapsed}
        />
      </div>

      {/* Mobile Sidebar - Overlay/Sheet component handled in AppSidebar */}
      <div className="md:hidden">
        <AppSidebar isCollapsed={false} onCollapseChange={() => {}} />
      </div>

      {/* Main content area */}
      <div
        className={`flex-1 flex flex-col transition-all duration-300 ${
          // On mobile: full width, on desktop: account for sidebar
          "w-full md:ml-16 md:w-[calc(100%-4rem)]"
        } ${
          // On desktop, adjust for expanded sidebar
          !isSidebarCollapsed ? "md:ml-64 md:w-[calc(100%-16rem)]" : ""
        }`}
      >
        {/* Topbar */}
        <header className="sticky top-0 z-20 h-16 border-b bg-background px-4 md:px-6">
          <AppTopbar />
        </header>

        {/* Page content */}
        <main className="flex-1 w-full">{children}</main>
      </div>
    </div>
  );
}
